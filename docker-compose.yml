version: '3.9'

services:
  agents:
    build:
      context: ./agents
      dockerfile: Dockerfile
    env_file:
      - ./agents/.env
    ports:
      - "${PORT:-8000}:8000"
    depends_on:
      - redis

  redis:
    image: redis:7
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --save 60 1 --loglevel warning
    # Optional volume if you want persistence
    # volumes:
    #   - redis_data:/data