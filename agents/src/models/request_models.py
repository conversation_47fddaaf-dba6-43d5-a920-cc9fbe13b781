from pydantic import BaseModel

class UserInput(BaseModel):
    """
    Model for user input to the chat system
    """
    input_data: str
    user_id: str = "default_user"  # Allow multiple users
    channel: Optional[str] = "web"  # Channel (web or whatsapp)

class ClassificationRequest(BaseModel):
    """
    Model for classification requests
    """
    input_data: str
    user_id: str = "default_user"
    channel: Optional[str] = "web"  # Channel (web or whatsapp)

class CheckupRequest(BaseModel):
    """
    Model for scheduling checkup messages
    """
    user_id: str
    minutes_from_now: int
    message: str

class WeeklySummaryRequest(BaseModel):
    """
    Model for requesting a weekly summary
    """
    user_id: str = "default_user"

class WhatsAppMappingRequest(BaseModel):
    """
    Model for mapping WhatsApp numbers to user IDs
    """
    whatsapp_number: str
    user_id: str
